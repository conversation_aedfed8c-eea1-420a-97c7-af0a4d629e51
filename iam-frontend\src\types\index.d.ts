// User types
export interface User {
  id: string
  username: string
  email: string
  firstName: string
  lastName: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  groups?: Group[]
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  firstName: string
  lastName: string
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  firstName?: string
  lastName?: string
  isActive?: boolean
}

// Group types
export interface Group {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  users?: User[]
  roles?: Role[]
}

export interface CreateGroupRequest {
  name: string
  description: string
}

export interface UpdateGroupRequest {
  name?: string
  description?: string
}

// Role types
export interface Role {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  groups?: Group[]
  permissions?: Permission[]
}

export interface CreateRoleRequest {
  name: string
  description: string
}

export interface UpdateRoleRequest {
  name?: string
  description?: string
}

// Module types
export interface Module {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  permissions?: Permission[]
}

export interface CreateModuleRequest {
  name: string
  description: string
}

export interface UpdateModuleRequest {
  name?: string
  description?: string
}

// Permission types
export interface Permission {
  id: string
  action: string
  moduleId: string
  module?: Module
  createdAt: string
  updatedAt: string
  roles?: Role[]
}

export interface CreatePermissionRequest {
  action: string
  moduleId: string
}

export interface UpdatePermissionRequest {
  action?: string
  moduleId?: string
}

// Authentication types
export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  firstName: string
  lastName: string
}

export interface AuthResponse {
  success: boolean
  message: string
  token?: string
  user?: User
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean
  message: string
  data?: T
  error?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  message: string
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Permission check types
export interface PermissionCheck {
  module: string
  action: string
}

export interface UserPermissions {
  permissions: Array<{
    module: string
    action: string
  }>
}

// Assignment types
export interface AssignUsersToGroupRequest {
  userIds: string[]
}

export interface AssignRolesToGroupRequest {
  roleIds: string[]
}

export interface AssignPermissionsToRoleRequest {
  permissionIds: string[]
}

// Form types
export interface FormErrors {
  [key: string]: string
}

export interface LoadingState {
  [key: string]: boolean
}

// Navigation types
export interface NavItem {
  name: string
  path: string
  icon?: string
  requiresPermission?: {
    module: string
    action: string
  }
}
